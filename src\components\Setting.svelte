<script lang="ts">
    import { onMount } from "svelte";
    import { showMessage, getFrontend, Menu } from "siyuan";
    import type { ISettingItem, SettingType } from "../core/types";
    // @ts-ignore
    import Tabs from './Tabs.svelte';

    const isMobile = () => getFrontend().endsWith('mobile'); // 运行环境判断
    import { notebook } from "../core/document";

    import { QRCodeManager, isBilibiliAvailable } from "../core/bilibili";
    import { LicenseManager, type LicenseInfo } from "../core/license";
    import { WebDAVManager } from "../core/webdav";

    export let group: string;
    export let config: any;
    export let i18n: any;
    export let activeTabId = 'settings';
    export let plugin: any;
    
    // 配置管理 - 极简版
    const getConfig = async () => await plugin.loadData('config.json') || {};
    const saveConfig = async (cfg) => { await plugin.saveData('config.json', cfg, 2); window.dispatchEvent(new CustomEvent('configUpdated', { detail: cfg })); };

    // 数据库操作 - 极简版
    const processDbId = async (id: string) => { if (!id || !/^\d{14}-[a-z0-9]{7}$/.test(id)) return { id, avId: '' }; const avId = (await fetch('/api/query/sql', { method: 'POST', body: JSON.stringify({ stmt: `SELECT markdown FROM blocks WHERE type='av' AND id='${id}'` }) }).then(r => r.json()).catch(() => ({ data: [] }))).data?.[0]?.markdown?.match(/data-av-id="([^"]+)"/)?.[1]; return { id, avId: avId || id }; };
    const initDb = async (id: string) => { try { const { avId } = await processDbId(id); return !!(await fetch('/api/av/getAttributeView', { method: 'POST', body: JSON.stringify({ id: avId }) }).then(r => r.json()).catch(() => ({ code: -1 }))).code === 0; } catch { return false; } };
    
    // 默认值定义
    const DEFAULTS = {
        proLicense: null,
        webdavAccounts: [],
        openlistAccounts: [],
        bilibiliLogin: null,
        openMode: "default",
        playerType: "built-in",
        playerPath: "PotPlayerMini64.exe",
        volume: 70,
        speed: 100,
        showSubtitles: false,
        enableDanmaku: false,
        loopCount: 3,
        pauseAfterLoop: false,
        loopPlaylist: false,
        loopSingle: false,
        insertMode: "updateBlock",
        notebook: { id: '', name: '' },
        parentDoc: { id: '', name: '', path: '' },
        enableDatabase: false,
        playlistDb: { id: '', avId: '' },
        playlistView: { mode: 'detailed', tab: '目录', expanded: [] },
        screenshotWithTimestamp: false,
        linkFormat: "- [😄标题 艺术家 字幕 时间](链接)",
        mediaNotesTemplate: "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n-  链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容："
    };
    
    // 状态和数据
    let activeTab = 'account';
    let state: any = {};
    let settingItems: ISettingItem[] = [];
    let notebooks = [];
    let notebookOptions = [];
    let qrCodeManager: QRCodeManager | null = null;
    let qrcode = { data: '', key: '' };
    // 许可证状态
    let licenseCode = '';
    let currentLicense: LicenseInfo | null = null;

    // 账号管理状态
    let accounts: any[] = [];
    let editingAccount: string | null = null;

    // 复制用户信息
    (globalThis as any).copyUserInfo = async () => {
        const user = await LicenseManager.getSiYuanUserInfo();
        if (!user) return showMessage('请先登录思源账号', 3000, 'error');
        await navigator.clipboard.writeText(JSON.stringify({userId: user.userId, userName: user.userName}));
        showMessage('用户信息已复制', 2000, 'info');
    };

    // 账号类型配置
    const ACCOUNT_TYPES = {
        webdav: { name: 'WebDAV', fields: ['server', 'username', 'password'] },
        openlist: { name: 'OpenList', fields: ['server', 'username', 'password'] },
        ...(isBilibiliAvailable() ? { bilibili: { name: 'B站', fields: [] } } : {})
    };

    // 账号管理函数
    const loadAccounts = () => {
        accounts = [
            ...(state.webdavAccounts || []).map(acc => ({ ...acc, type: 'webdav' })),
            ...(state.openlistAccounts || []).map(acc => ({ ...acc, type: 'openlist' })),
            ...(state.bilibiliLogin ? [{ id: 'bili_' + state.bilibiliLogin.mid, type: 'bilibili', ...state.bilibiliLogin }] : [])
        ];
    };
    
    // 标签页定义
    const tabs = [
        { id: 'account', name: i18n.setting.tabs?.account || '账号' },
        { id: 'player', name: i18n.setting.tabs?.player || '播放器' },
        { id: 'general', name: i18n.setting.tabs?.general || '通用' }
    ];
    
    // 通用账号描述生成器
    const accDesc = (icon, name, status, statusColor, info1, info2) =>
        ({ icon, name, status, statusColor, info1, info2 });
    
    // 描述渲染
    const renderDesc = (d) => d?.icon ?
        `${d.icon.startsWith('#') ? `<svg class="acc-icon"><use xlink:href="${d.icon}"></use></svg>` : `<img src="${d.icon}" class="acc-icon">`}
        <div class="acc-info"><b>${d.name}</b> <span style="color:${d.statusColor}">${d.status}</span><br><small>${d.info1}</small><br><small class="acc-muted">${d.info2}</small></div>` : d;

    // 创建默认设置项
    function createSettings(state): ISettingItem[] {
        return [
        // Pro账号
            { key: "proLicense", type: "account" as SettingType, tab: "account",
                description: currentLicense && currentLicense.isValid ? (() => {
                    const typeIcons = { trial: '#iconVIP', annual: '#iconVIP', dragon: '#iconDragon' };
                    const exp = currentLicense.expiresAt === 0 ? (i18n.pro?.status?.permanent || '永久有效') : new Date(currentLicense.expiresAt).toLocaleDateString();
                    const statusText = i18n.pro?.status?.[currentLicense.type] || currentLicense.type;
                    return accDesc(typeIcons[currentLicense.type] || '#iconVIP', `${currentLicense.userName} (${statusText})`, '', '', `用户ID: ${currentLicense.userId}`, `期限: ${exp}`);
                })() : accDesc('#iconVIP', licenseCode ? 'Pro会员' : '申请7天体验', '', '', licenseCode ? '输入激活码后开启' : '享受完整功能体验', '点击开关即可开启'),
                accountData: { type: 'pro', isValid: !!(currentLicense && currentLicense.isValid) } },

            // 账号项（按类型排序）
            ...accounts.sort((a, b) => {
                const order = { webdav: 1, openlist: 2, bilibili: 3 };
                return (order[a.type] || 999) - (order[b.type] || 999);
            }).map(acc => ({
                key: `account_${acc.id}`, type: "account" as SettingType, tab: "account",
                description: accDesc(
                    acc.type === 'bilibili' ? acc.face || '#iconBili' : '#iconCloud',
                    acc.uname || acc.username || ACCOUNT_TYPES[acc.type]?.name,
                    acc.type === 'bilibili' ? `LV${acc.level_info?.current_level}` : '已连接',
                    '#4caf50',
                    acc.type === 'bilibili' ? `UID ${acc.mid}` : acc.server,
                    acc.type === 'bilibili' ? `硬币 ${acc.money}` : `用户: ${acc.username}`
                ),
                accountData: acc
            })),
          
            // 播放器设置
            { key: "openMode", value: state.openMode, type: "select" as SettingType, tab: "player",
              title: i18n.setting.items.openMode?.title || "打开方式",
              description: i18n.setting.items.openMode?.description,
              displayCondition: () => !isMobile(), // 移动端隐藏打开方式选项
              options: [
                { label: i18n.setting.items.openMode?.options?.default || "新标签", value: "default" },
                { label: i18n.setting.items.openMode?.options?.right || "右侧新标签", value: "right" },
                { label: i18n.setting.items.openMode?.options?.bottom || "底部新标签", value: "bottom" },
                { label: i18n.setting.items.openMode?.options?.window || "新窗口", value: "window" }
              ] },
            { key: "playerType", value: state.playerType, type: "select" as SettingType, tab: "player",
              title: i18n.setting.items.playerType.title,
              description: i18n.setting.items.playerType.description,
              options: [
                { label: i18n.setting.items.playerType.builtIn, value: "built-in" },
                { label: i18n.setting.items.playerType.potPlayer, value: "potplayer" },
                { label: i18n.setting.items.playerType.browser, value: "browser" }
              ] },
            { key: "playerPath", value: state.playerPath, type: "textarea" as SettingType, tab: "player",
              displayCondition: () => settingItems.find(i => i.key === 'playerType')?.value === 'potplayer',
              title: i18n.setting.items?.playerPath?.title || "PotPlayer路径",
              description: i18n.setting.items?.playerPath?.description || "设置PotPlayer可执行文件路径",
              rows: 1 },
            { key: "volume", value: state.volume, type: "slider" as SettingType, tab: "player",
              title: i18n.setting.items.volume.title,
              description: i18n.setting.items.volume.description,
              slider: { min: 0, max: 100, step: 1 } },
            { key: "speed", value: state.speed, type: "slider" as SettingType, tab: "player",
              title: i18n.setting.items.speed.title,
              description: i18n.setting.items.speed.description,
              slider: { min: 50, max: 500, step: 50 } },
            { key: "showSubtitles", value: state.showSubtitles, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items.showSubtitles?.title || "显示字幕",
              description: i18n.setting.items.showSubtitles?.description },
            { key: "enableDanmaku", value: state.enableDanmaku, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items.enableDanmaku?.title || "启用弹幕",
              description: i18n.setting.items.enableDanmaku?.description },
            { key: "loopCount", value: state.loopCount, type: "slider" as SettingType, tab: "player",
              title: i18n.setting.items.loopCount.title,
              description: i18n.setting.items.loopCount.description,
              slider: { min: 1, max: 10, step: 1 } },
            { key: "pauseAfterLoop", value: state.pauseAfterLoop, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items.pauseAfterLoop?.title || "片段循环后暂停",
              description: i18n.setting.items.pauseAfterLoop?.description },
            { key: "loopPlaylist", value: state.loopPlaylist, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items?.loopPlaylist?.title || "循环列表",
              description: i18n.setting.items?.loopPlaylist?.description || "播放完列表后从头开始",
              onChange: (v) => (state.loopPlaylist = v, v && (state.loopSingle = false)) },
            { key: "loopSingle", value: state.loopSingle, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items?.loopSingle?.title || "单项循环",
              description: i18n.setting.items?.loopSingle?.description || "重复播放当前媒体",
              onChange: (v) => (state.loopSingle = v, v && (state.loopPlaylist = false)) },
            
            // 通用设置
            { key: "enableDatabase", value: state.enableDatabase, type: "checkbox" as SettingType, tab: "general",
              title: i18n.setting?.items?.enableDatabase?.title || "绑定数据库",
              description: i18n.setting?.items?.enableDatabase?.description || "启用播放列表数据库功能，用于保存和管理媒体项目" },
            { key: "playlistDb", value: state.playlistDb?.id || "", type: "textarea" as SettingType, tab: "general",
              displayCondition: () => state.enableDatabase,
              title: i18n.setting?.items?.playlistDb?.title || "播放列表数据库",
              description: state.playlistDb?.avId
                ? (i18n.setting?.items?.playlistDb?.avIdDescription?.replace('${avId}', state.playlistDb.avId) || `属性视图ID: ${state.playlistDb.avId}`)
                : (i18n.setting?.items?.playlistDb?.description || "输入数据库ID（支持数据库块ID或数据库ID/avid，格式：14位数字-7位字符）"),
              onChange: async (v) => {
                const result = v ? await processDbId(v) : { id: '', avId: '' };
                state.playlistDb = result;
                if (result.avId) await initDb(v).catch(() => {});
                settingItems = createSettings(state);
              },
              rows: 1 },
            { key: "targetDocumentSearch", value: "", type: "text" as SettingType, tab: "general",
              title: i18n.setting.items?.mediaNoteLocation?.search?.title || "媒体笔记创建位置",
              description: i18n.setting.items?.mediaNoteLocation?.search?.description || "输入关键字后按回车搜索文档",
              onKeydown: async (e) => {
                if (e.key === 'Enter') {
                  const result = await notebook.searchAndUpdate(e.target.value, state, { getConfig, saveConfig });
                  if (result.success && result.docs) {
                    notebookOptions = [...notebooks.map(nb => ({ label: nb.name, value: nb.id })), ...result.docs.map(doc => ({ label: doc.hPath || '无标题', value: doc.path?.split('/').pop()?.replace('.sy', '') || doc.id, notebook: doc.box, path: doc.path?.replace('.sy', '') || '' }))];
                    settingItems = createSettings(state);
                  }
                }
              } },
            { key: "targetNotebook", value: state.parentDoc?.id || state.notebook?.id || "", type: "select" as SettingType, tab: "general",
              title: i18n.setting.items?.mediaNoteLocation?.target?.title || "媒体笔记目标笔记本/文档",
              description: state.parentDoc?.id ? `目标文档：${state.parentDoc.name}` : (state.notebook?.id ? `目标笔记本：${state.notebook.name}` : (i18n.setting.items?.mediaNoteLocation?.target?.description || "选择创建媒体笔记的目标笔记本")),
              onChange: (v) => {
                const notebook = notebooks.find(nb => nb.id === v);
                const docOption = notebookOptions.find(opt => opt.value === v);
                if (notebook) { state.notebook = { id: v, name: notebook.name }; state.parentDoc = { id: '', name: '', path: '' }; }
                else if (docOption) { state.parentDoc = { id: v, name: docOption.label, path: docOption.path || '' }; state.notebook = { id: docOption.notebook || '', name: '' }; }
              },
              options: notebookOptions.length ? notebookOptions : notebooks.map(nb => ({ label: nb.name, value: nb.id })) },
            { key: "insertMode", value: state.insertMode, type: "select" as SettingType, tab: "general",
              title: i18n.setting.items.insertMode?.title || "插入方式",
              description: i18n.setting.items.insertMode?.description || "选择时间戳和笔记的插入方式",
              onChange: (v) => state.insertMode = v,
              options: [
                { label: i18n.setting.items.insertMode?.insertBlock || "插入光标处", value: "insertBlock" },
                { label: i18n.setting.items.insertMode?.appendBlock || "追加到块末尾", value: "appendBlock" },
                { label: i18n.setting.items.insertMode?.prependBlock || "添加到块开头", value: "prependBlock" },
                { label: i18n.setting.items.insertMode?.updateBlock || "更新当前块", value: "updateBlock" },
                { label: i18n.setting.items.insertMode?.prependDoc || "插入到文档顶部", value: "prependDoc" },
                { label: i18n.setting.items.insertMode?.appendDoc || "插入到文档底部", value: "appendDoc" },
                { label: i18n.setting.items.insertMode?.clipboard || "复制到剪贴板", value: "clipboard" }
              ] },
            { key: "screenshotWithTimestamp", value: state.screenshotWithTimestamp, type: "checkbox" as SettingType, tab: "general",
              title: i18n.setting.items?.screenshotWithTimestamp?.title || "截图包含时间戳",
              description: i18n.setting.items?.screenshotWithTimestamp?.description || "启用后，截图功能也会添加时间戳链接" },
            { key: "linkFormat", value: state.linkFormat || "- [😄标题 艺术家 字幕 时间](链接)", 
              type: "textarea" as SettingType, tab: "general",
              title: i18n.setting.items?.linkFormat?.title || "链接格式",
              description: i18n.setting.items?.linkFormat?.description || "支持变量：标题、时间、艺术家、链接、字幕、截图",
              rows: 1 },
            { key: "mediaNotesTemplate",
              value: state.mediaNotesTemplate || "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n- 🔗 链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容：",
              type: "textarea" as SettingType, tab: "general",
              title: i18n.setting.items?.mediaNotesTemplate?.title || "媒体笔记模板",
              description: i18n.setting.items?.mediaNotesTemplate?.description || "支持变量：标题、时间、艺术家、链接、时长、封面、类型、ID、日期、时间戳",
              rows: 9 }
        ];
    }

    // 初始化
    async function refreshSettings() {
        const cfg = await getConfig();
        state = { ...DEFAULTS, ...(cfg.settings || {}) };
        // 加载许可证
        currentLicense = await LicenseManager.load(plugin);

        // 体验会员启动提示（激励购买）
        if (currentLicense?.type === 'trial') {
            const expireDate = new Date(currentLicense.expiresAt).toLocaleDateString();
            showMessage(`体验会员已激活（到期：${expireDate}）🎯 升级享受完整功能`, 4000, 'info');
        }
        try { notebooks = await notebook.getList?.() || []; } catch {}
        notebookOptions = [
            ...notebooks.map(nb => ({ label: nb.name, value: nb.id })),
            ...(state.parentDoc?.id ? [{ label: state.parentDoc.name, value: state.parentDoc.id, path: state.parentDoc.path }] : [])
        ];
        loadAccounts();
        settingItems = createSettings(state);
    }


    // 重置单个设置项
    function resetItem(key) {
        state[key] = DEFAULTS[key];
        settingItems = createSettings(state);
    }

    // 设置项变更处理
    async function handleChange(e, item) {
        const v = e.target.type === 'checkbox' 
            ? e.target.checked 
            : e.target.value;
        if (item.onChange) {await item.onChange(v);} 
        else {state[item.key] = v;}
        settingItems = createSettings(state);
        const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
    }

    $: if (activeTab) refreshSettings();

    onMount(refreshSettings);
</script>

<div class="panel common-panel" data-name={group}>
    <!-- 统一导航 -->
    <Tabs {activeTabId} {i18n}>
        <svelte:fragment slot="controls">
            <span class="panel-count">{tabs.find(tab => tab.id === activeTab)?.name || i18n.setting.description}</span>
        </svelte:fragment>
    </Tabs>

    <div class="panel-tabs">
        {#each tabs as tab}
            <button class="tab" class:active={activeTab === tab.id} on:click={() => activeTab = tab.id}>{tab.name}</button>
        {/each}
    </div>

    <div class="panel-content">
        {#each settingItems as item (item.key)}
            {#if item.tab === activeTab && (!item.displayCondition || item.displayCondition(state))}
            <!-- 账号分组标题 -->
            {#if item.type === 'account' && item.accountData && item.accountData.type !== 'pro'}
                {@const prevItem = settingItems[settingItems.indexOf(item) - 1]}
                {#if !prevItem || !prevItem.accountData || prevItem.accountData.type !== item.accountData.type}
                    <div style="margin: 20px 0 8px 0; color: var(--b3-theme-on-surface-light); font-size: 14px; font-weight: 500;">
                        {ACCOUNT_TYPES[item.accountData.type]?.name}
                    </div>
                {/if}
            {/if}

            <div class="setting-item setting-item-{item.type}" data-key={item.key}>
                <div class="setting-info">
                    {#if item.type !== 'account'}
                        <div class="setting-title">{item.title}</div>
                    {/if}
                    {#if item.description}
                        <div class="setting-description {item.description?.icon ? 'acc-desc' : ''}">
                            {@html renderDesc(item.description)}
                        </div>
                    {/if}
                    
                    {#if item.type === 'slider'}
                        <div class="slider-wrapper">
                            <input type="range"
                                min={item.slider?.min ?? 0}
                                max={item.slider?.max ?? 100}
                                step={item.slider?.step ?? 1}
                                value={state[item.key]}
                                on:input={(e) => handleChange(e, item)}>
                            <span class="slider-value">{item.key === 'speed' ? Number(state[item.key]) / 100 + 'x' : state[item.key]}</span>
                        </div>
                    {:else if item.type === 'text'}
                        <input
                            type="text"
                            class="b3-text-field fn__block"
                            value={String(item.value)}
                            on:input={(e) => handleChange(e, item)}
                            on:keydown={(e) => item.onKeydown && item.onKeydown(e)}>
                        <span class="clear-icon" on:click={() => resetItem(item.key)}>
                            <svg class="icon"><use xlink:href="#iconRefresh"></use></svg>
                        </span>
                    {:else if item.type === 'textarea'}
                        <textarea
                            class="b3-text-field fn__block"
                            rows={item.rows || 4}
                            value={String(item.value)}
                            on:input={(e) => handleChange(e, item)}></textarea>
                        <span class="clear-icon" on:click={() => resetItem(item.key)}>
                            <svg class="icon"><use xlink:href="#iconRefresh"></use></svg>
                        </span>
                    {:else if item.type === 'images'}
                        <div class="image-gallery">
                            {#each Array.isArray(item.value) ? item.value : [] as image}
                                <img src={image.url} alt={image.caption || item.title} class="image-item">
                            {/each}
                        </div>
                    {/if}
                </div>
                
                <div class="setting-control">
                    {#if item.type === 'checkbox'}
                        <label class="checkbox-wrapper">
                            <input type="checkbox" checked={Boolean(item.value)} on:change={(e) => handleChange(e, item)}>
                            <span class="checkbox-custom"></span>
                        </label>
                    {:else if item.type === 'select'}
                        <select class="select-wrapper" style="max-width: 200px; width: 200px;" value={item.value} on:change={(e) => handleChange(e, item)}>
                            {#each item.options || [] as option}
                                <option value={option.value} title={option.label}>{option.label.length > 30 ? option.label.slice(0, 30) + '...' : option.label}</option>
                            {/each}
                        </select>
                    {:else if item.type === 'button'}
                        <button class="b3-button b3-button--outline {item.buttonStyle || ''}" on:click={() => item.onAction && item.onAction()}>
                            {item.buttonText || '操作'}
                        </button>
                    {:else if item.type === 'account'}
                        <button class="b3-button b3-button--text" on:click|preventDefault|stopPropagation={(e) => {
                            const m = new Menu('accountMenu');
                            if (item.accountData.type === 'pro') {
                                if (item.accountData.isValid) {
                                    m.addItem({ icon: 'iconClose', label: '关闭Pro', click: async () => {
                                        currentLicense = null;
                                        settingItems = createSettings(state);
                                        showMessage('Pro功能已关闭', 2000, 'info');
                                    }});
                                } else {
                                    m.addItem({ icon: 'iconEdit', label: '激活Pro', click: () => editingAccount = 'pro' });
                                }
                                m.addItem({ icon: 'iconInfo', label: '购买会员', click: () => window.open('https://afdian.tv/a/mmomm', '_blank') });
                            } else {
                                m.addItem({ icon: 'iconEdit', label: '编辑', click: () => {
                                    editingAccount = item.accountData.id;
                                    // 加载现有数据
                                    state.editingData = { ...item.accountData };
                                }});
                                m.addItem({ icon: 'iconTrashcan', label: '删除', click: async () => {
                                    if (item.accountData.type === 'bilibili') {
                                        state.bilibiliLogin = null;
                                        qrcode = { data: '', key: '' };
                                        qrCodeManager?.stopPolling();
                                    } else if (item.accountData.type === 'webdav') {
                                        state.webdavAccounts = (state.webdavAccounts || []).filter(a => a.id !== item.accountData.id);
                                    } else if (item.accountData.type === 'openlist') {
                                        state.openlistAccounts = (state.openlistAccounts || []).filter(a => a.id !== item.accountData.id);
                                    }
                                    const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
                                    loadAccounts(); settingItems = createSettings(state);
                                    showMessage('账号已删除', 2000, 'info');
                                }});
                            }
                            m.open({ x: e.clientX, y: e.clientY });
                        }}>⋯</button>
                    {/if}
                </div>
            </div>


            {/if}
        {/each}



        <!-- 账号编辑表单 -->
        {#if activeTab === 'account' && editingAccount}
            {@const isEditing = accounts.find(a => a.id === editingAccount)}
            {@const accountType = isEditing ? isEditing.type : editingAccount}
            {@const saveAccount = async () => {
                const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
                loadAccounts(); settingItems = createSettings(state);
                editingAccount = null; state.editingData = {};
            }}
            <div class="setting-item" style="margin: 16px 0; border: 1px solid var(--b3-border-color); border-radius: 4px; padding: 16px;">
                <div class="setting-title">{accountType === 'pro' ? 'Pro会员激活' : `${isEditing ? '编辑' : '添加'}${ACCOUNT_TYPES[accountType]?.name}账号`}</div>
                {#if accountType === 'pro'}
                    <input type="text" class="b3-text-field fn__block" bind:value={licenseCode} placeholder="请输入激活码" style="margin: 8px 0;">
                    <button class="b3-button b3-button--text" on:click={async () => {
                        const r = await LicenseManager.activate(licenseCode, plugin);
                        currentLicense = r.license || null;
                        if (r.success && licenseCode) licenseCode = '';
                        showMessage(r.message || r.error, r.success ? 2000 : (r.isHtml ? 0 : 3000), r.success ? 'info' : 'error');
                        settingItems = createSettings(state);
                        await saveAccount();
                    }}>激活</button>
                {:else if accountType === 'bilibili'}
                    {#if qrcode?.data}
                        <img src={qrcode.data} alt="B站登录二维码" style="width: 200px; margin: 10px 0;">
                        <p>{qrcode.message || '等待扫码'}</p>
                    {:else}
                        <button class="b3-button b3-button--outline" on:click={async () => {
                            qrCodeManager ||= new QRCodeManager(q => qrcode = q, async loginData => {
                                state.bilibiliLogin = loginData;
                                await saveAccount();
                                qrCodeManager?.stopPolling();
                                showMessage('B站账号登录成功', 2000, 'info');
                            });
                            await qrCodeManager.startLogin();
                        }}>开始登录</button>
                    {/if}
                {:else}
                    {#each (ACCOUNT_TYPES[accountType] || { fields: [] }).fields as field}
                        <label style="display: block; margin: 8px 0 4px;">{field === 'server' ? '服务器地址' : field === 'username' ? '用户名' : '密码'}</label>
                        <input type={field === 'password' ? 'password' : 'text'} class="b3-text-field fn__block"
                               value={state.editingData?.[field] || ''}
                               on:input={(e) => { state.editingData = state.editingData || {}; state.editingData[field] = e.target.value; }}
                               placeholder={field === 'server' ? 'https://your-server.com' : field}>
                    {/each}
                    <button class="b3-button b3-button--text" style="margin-top: 12px;" on:click={async () => {
                        const data = state.editingData || {};
                        if (accountType === 'webdav') {
                            const result = await WebDAVManager.checkConnection(data);
                            if (!result.connected) { showMessage(result.message, 3000, 'error'); return; }
                            state.webdavAccounts = state.webdavAccounts || [];
                            const index = state.webdavAccounts.findIndex(a => a.id === editingAccount);
                            if (index >= 0) state.webdavAccounts[index] = { ...state.webdavAccounts[index], ...data };
                            else state.webdavAccounts.push({ ...data, id: Date.now().toString() });
                            showMessage('WebDAV账号保存成功', 2000, 'info');
                        } else {
                            if (!data.server || !data.username) { showMessage('服务器地址和用户名不能为空', 3000, 'error'); return; }
                            state.openlistAccounts = state.openlistAccounts || [];
                            const index = state.openlistAccounts.findIndex(a => a.id === editingAccount);
                            if (index >= 0) state.openlistAccounts[index] = { ...state.openlistAccounts[index], ...data };
                            else state.openlistAccounts.push({ ...data, id: Date.now().toString() });
                            showMessage('OpenList账号保存成功', 2000, 'info');
                        }
                        await saveAccount();
                    }}>保存</button>
                {/if}
                <button class="b3-button b3-button--cancel" on:click={() => { editingAccount = null; state.editingData = {}; }}>取消</button>
            </div>
        {/if}

        <!-- 添加账号按钮 -->
        {#if activeTab === 'account'}
            <div class="setting-item" style="margin-top: 20px;">
                <div class="setting-info">
                    <div class="setting-title">添加账号</div>
                    <div class="setting-description">添加WebDAV、OpenList或B站账号</div>
                </div>
                <div class="setting-control">
                    <button class="b3-button b3-button--outline" on:click|preventDefault|stopPropagation={(e) => {
                        const m = new Menu('addAccountMenu');
                        Object.entries(ACCOUNT_TYPES).forEach(([type, config]) => {
                            m.addItem({ icon: type === 'bilibili' ? 'iconBili' : 'iconCloud', label: config.name, click: () => {
                                editingAccount = type;
                                state.editingData = {}; // 清空编辑数据
                            }});
                        });
                        m.open({ x: e.clientX, y: e.clientY });
                    }}>添加账号</button>
                </div>
            </div>
        {/if}


    </div>
</div>

