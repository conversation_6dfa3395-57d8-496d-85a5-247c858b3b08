<script lang="ts">
    import { onMount } from "svelte";
    import { showMessage, getFrontend, Menu } from "siyuan";
    import type { ISettingItem, SettingType } from "../core/types";
    // @ts-ignore
    import Tabs from './Tabs.svelte';

    const isMobile = () => getFrontend().endsWith('mobile'); // 运行环境判断
    import { notebook } from "../core/document";

    import { QRCodeManager, isBilibiliAvailable } from "../core/bilibili";
    import { LicenseManager, type LicenseInfo } from "../core/license";
    import { WebDAVManager } from "../core/webdav";

    export let group: string;
    export let config: any;
    export let i18n: any;
    export let activeTabId = 'settings';
    export let plugin: any;
    
    // 配置管理 - 极简版
    const getConfig = async () => await plugin.loadData('config.json') || {};
    const saveConfig = async (cfg) => { await plugin.saveData('config.json', cfg, 2); window.dispatchEvent(new CustomEvent('configUpdated', { detail: cfg })); };

    // 数据库操作 - 极简版
    const processDbId = async (id: string) => { if (!id || !/^\d{14}-[a-z0-9]{7}$/.test(id)) return { id, avId: '' }; const avId = (await fetch('/api/query/sql', { method: 'POST', body: JSON.stringify({ stmt: `SELECT markdown FROM blocks WHERE type='av' AND id='${id}'` }) }).then(r => r.json()).catch(() => ({ data: [] }))).data?.[0]?.markdown?.match(/data-av-id="([^"]+)"/)?.[1]; return { id, avId: avId || id }; };
    const initDb = async (id: string) => { try { const { avId } = await processDbId(id); return !!(await fetch('/api/av/getAttributeView', { method: 'POST', body: JSON.stringify({ id: avId }) }).then(r => r.json()).catch(() => ({ code: -1 }))).code === 0; } catch { return false; } };
    
    // 默认值定义
    const DEFAULTS = {
        proLicense: null,
        webdavAccounts: [],
        openlistAccounts: [],
        bilibiliLogin: null,
        openMode: "default",
        playerType: "built-in",
        playerPath: "PotPlayerMini64.exe",
        volume: 70,
        speed: 100,
        showSubtitles: false,
        enableDanmaku: false,
        loopCount: 3,
        pauseAfterLoop: false,
        loopPlaylist: false,
        loopSingle: false,
        insertMode: "updateBlock",
        notebook: { id: '', name: '' },
        parentDoc: { id: '', name: '', path: '' },
        enableDatabase: false,
        playlistDb: { id: '', avId: '' },
        playlistView: { mode: 'detailed', tab: '目录', expanded: [] },
        screenshotWithTimestamp: false,
        linkFormat: "- [😄标题 艺术家 字幕 时间](链接)",
        mediaNotesTemplate: "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n-  链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容："
    };
    
    // 状态和数据
    let activeTab = 'account';
    let state: any = {};
    let settingItems: ISettingItem[] = [];
    let notebooks = [];
    let notebookOptions = [];
    let qrCodeManager: QRCodeManager | null = null;
    let qrcode = { data: '', key: '' };
    // 许可证状态
    let licenseCode = '';
    let currentLicense: LicenseInfo | null = null;

    // 账号管理状态
    let accounts: any[] = [];
    let editingAccount: string | null = null;
    let showAddMenu = false;
    let currentEditAccount: any = {};

    // 响应式计算编辑状态
    $: isNewAccount = editingAccount?.startsWith('new_') || false;
    $: editAccountType = isNewAccount ? editingAccount?.replace('new_', '') : accounts.find(acc => acc.id === editingAccount)?.type;
    $: {
        if (editingAccount) {
            if (isNewAccount) {
                currentEditAccount = { type: editAccountType };
            } else {
                const found = accounts.find(acc => acc.id === editingAccount);
                currentEditAccount = found ? { ...found } : {};
            }
        }
    }

    // 复制用户信息
    (globalThis as any).copyUserInfo = async () => {
        const user = await LicenseManager.getSiYuanUserInfo();
        if (!user) return showMessage('请先登录思源账号', 3000, 'error');
        await navigator.clipboard.writeText(JSON.stringify({userId: user.userId, userName: user.userName}));
        showMessage('用户信息已复制', 2000, 'info');
    };

    // 账号类型配置
    const getAccountTypes = () => ({
        webdav: { name: 'WebDAV', icon: '/plugins/siyuan-media-player/assets/images/webdav.svg', fields: ['server', 'username', 'password'] },
        openlist: { name: 'OpenList', icon: '/plugins/siyuan-media-player/assets/images/openlist.svg', fields: ['server', 'username', 'password'] },
        ...(isBilibiliAvailable() ? { bilibili: { name: 'B站', icon: '#iconBili', fields: [] } } : {})
    });

    // 账号管理函数
    const loadAccounts = () => {
        accounts = [
            ...(state.webdavAccounts || []).map(acc => ({ ...acc, type: 'webdav' })),
            ...(state.openlistAccounts || []).map(acc => ({ ...acc, type: 'openlist' })),
            ...(state.bilibiliLogin ? [{ id: 'bili_' + state.bilibiliLogin.mid, type: 'bilibili', name: state.bilibiliLogin.uname, ...state.bilibiliLogin }] : [])
        ];
    };

    const saveAccount = async (account: any) => {
        const { type, id, ...data } = account;
        if (type === 'webdav') {
            state.webdavAccounts = state.webdavAccounts || [];
            const index = state.webdavAccounts.findIndex(acc => acc.id === id);
            if (index >= 0) state.webdavAccounts[index] = data;
            else state.webdavAccounts.push({ id: id || Date.now().toString(), ...data });
        } else if (type === 'openlist') {
            state.openlistAccounts = state.openlistAccounts || [];
            const index = state.openlistAccounts.findIndex(acc => acc.id === id);
            if (index >= 0) state.openlistAccounts[index] = data;
            else state.openlistAccounts.push({ id: id || Date.now().toString(), ...data });
        }
        const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
        loadAccounts();
        editingAccount = null;
    };

    const deleteAccount = async (accountId: string) => {
        const account = accounts.find(acc => acc.id === accountId);
        if (!account) return;

        if (account.type === 'webdav') {
            state.webdavAccounts = (state.webdavAccounts || []).filter(acc => acc.id !== accountId);
        } else if (account.type === 'openlist') {
            state.openlistAccounts = (state.openlistAccounts || []).filter(acc => acc.id !== accountId);
        } else if (account.type === 'bilibili') {
            state.bilibiliLogin = null;
            qrcode = { data: '', key: '' };
            qrCodeManager?.stopPolling();
        }

        const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
        loadAccounts();
        showMessage('账号已删除', 2000, 'info');
    };

    const validateAccount = async (account: any) => {
        try {
            if (account.type === 'webdav') {
                const result = await WebDAVManager.checkConnection({ server: account.server, username: account.username, password: account.password });
                if (!result.connected) throw new Error(result.message);
            } else if (account.type === 'openlist') {
                // OpenList 验证逻辑
                if (!account.server || !account.username) throw new Error('服务器地址和用户名不能为空');
            }
            return { success: true, message: '验证成功' };
        } catch (error) {
            return { success: false, message: error.message };
        }
    };
    
    // 标签页定义
    const tabs = [
        { id: 'account', name: i18n.setting.tabs?.account || '账号' },
        { id: 'player', name: i18n.setting.tabs?.player || '播放器' },
        { id: 'general', name: i18n.setting.tabs?.general || '通用' }
    ];
    
    // 通用账号描述生成器
    const accDesc = (icon, name, status, statusColor, info1, info2) =>
        ({ icon, name, status, statusColor, info1, info2 });
    
    // 描述渲染
    const renderDesc = (d) => d?.icon ?
        `${d.icon.startsWith('#') ? `<svg class="acc-icon"><use xlink:href="${d.icon}"></use></svg>` : `<img src="${d.icon}" class="acc-icon">`}
        <div class="acc-info"><b>${d.name}</b> <span style="color:${d.statusColor}">${d.status}</span><br><small>${d.info1}</small><br><small class="acc-muted">${d.info2}</small></div>` : d;

    // 创建默认设置项
    function createSettings(state): ISettingItem[] {

        return [
        // Pro账号
            { key: "proLicense", type: "checkbox" as SettingType, tab: "account", title: i18n.pro?.title || "Media Player Pro",
                value: !!(currentLicense && currentLicense.isValid),
                description: currentLicense && currentLicense.isValid ? (() => {
                    const typeIcons = { trial: '#iconVIP', annual: '#iconVIP', dragon: '#iconDragon' };
                    const exp = currentLicense.expiresAt === 0 ? (i18n.pro?.status?.permanent || '永久有效') : new Date(currentLicense.expiresAt).toLocaleDateString();
                    const statusText = i18n.pro?.status?.[currentLicense.type] || currentLicense.type;
                    return accDesc(typeIcons[currentLicense.type] || '#iconVIP', `${currentLicense.userName} (${statusText})`, '', '', `用户ID: ${currentLicense.userId}`, `期限: ${exp}`);
                })() : accDesc('#iconVIP', licenseCode ? 'Pro会员' : '申请7天体验', '', '', licenseCode ? '输入激活码后开启' : '享受完整功能体验', '点击开关即可开启'),
                onChange: async (v) => {
                    if (!v) {
                        // 关闭Pro功能（不清除许可文件）
                        currentLicense = null;
                        showMessage('Pro功能已关闭', 2000, 'info');
                    } else {
                        // 激活Pro功能
                        const r = await LicenseManager.activate(licenseCode, plugin);
                        currentLicense = r.license || null;
                        if (r.success && licenseCode) licenseCode = '';
                        showMessage(r.message || r.error, r.success ? 2000 : (r.isHtml ? 0 : 3000), r.success ? 'info' : 'error');
                    }
                    settingItems = createSettings(state);
                    const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
                } },

            // 激活码输入
            { key: "licenseCode", type: "text" as SettingType, tab: "account",
                displayCondition: () => !(currentLicense && currentLicense.isValid),
                title: i18n.pro?.license?.title || "激活码", value: licenseCode,
                placeholder: i18n.pro?.license?.placeholder || "请输入激活码",
                onChange: (v) => { licenseCode = v; } },

            { key: "proPanel", type: "images" as SettingType, value: [
                { url: "/plugins/siyuan-media-player/assets/images/alipay.jpg", caption: "支付宝付款码" },
                { url: "/plugins/siyuan-media-player/assets/images/wechat.jpg", caption: "微信付款码" }
              ], tab: "account",
              displayCondition: () => !(currentLicense && currentLicense.isValid),
              title: i18n.pro?.priceTag || "¥ 18.00",
              description: i18n.pro?.priceDescription || `或 ¥ 16.00 + <a href="https://github.com/mm-o/siyuan-media-player" target="_blank" rel="noopener noreferrer">GitHub Star</a> 关注<br><span style="cursor: pointer; color: #4facfe; text-decoration: underline;" onclick="copyUserInfo()">点击复制用户信息</span>` },

            // 购买会员按钮
            { key: "buyMembership", type: "button" as SettingType, tab: "account",
              displayCondition: () => !(currentLicense && currentLicense.isValid),
              title: "购买正式会员",
              description: "前往爱发电平台购买正式会员，享受完整功能",
              value: "",
              buttonText: "前往爱发电购买",
              buttonStyle: "b3-button--text",
              onAction: () => {
                window.open('https://afdian.tv/a/mmomm', '_blank', 'noopener,noreferrer');
              }
            },


                       
            // 播放器设置
            { key: "openMode", value: state.openMode, type: "select" as SettingType, tab: "player",
              title: i18n.setting.items.openMode?.title || "打开方式",
              description: i18n.setting.items.openMode?.description,
              displayCondition: () => !isMobile(), // 移动端隐藏打开方式选项
              options: [
                { label: i18n.setting.items.openMode?.options?.default || "新标签", value: "default" },
                { label: i18n.setting.items.openMode?.options?.right || "右侧新标签", value: "right" },
                { label: i18n.setting.items.openMode?.options?.bottom || "底部新标签", value: "bottom" },
                { label: i18n.setting.items.openMode?.options?.window || "新窗口", value: "window" }
              ] },
            { key: "playerType", value: state.playerType, type: "select" as SettingType, tab: "player",
              title: i18n.setting.items.playerType.title,
              description: i18n.setting.items.playerType.description,
              options: [
                { label: i18n.setting.items.playerType.builtIn, value: "built-in" },
                { label: i18n.setting.items.playerType.potPlayer, value: "potplayer" },
                { label: i18n.setting.items.playerType.browser, value: "browser" }
              ] },
            { key: "playerPath", value: state.playerPath, type: "textarea" as SettingType, tab: "player",
              displayCondition: () => settingItems.find(i => i.key === 'playerType')?.value === 'potplayer',
              title: i18n.setting.items?.playerPath?.title || "PotPlayer路径",
              description: i18n.setting.items?.playerPath?.description || "设置PotPlayer可执行文件路径",
              rows: 1 },
            { key: "volume", value: state.volume, type: "slider" as SettingType, tab: "player",
              title: i18n.setting.items.volume.title,
              description: i18n.setting.items.volume.description,
              slider: { min: 0, max: 100, step: 1 } },
            { key: "speed", value: state.speed, type: "slider" as SettingType, tab: "player",
              title: i18n.setting.items.speed.title,
              description: i18n.setting.items.speed.description,
              slider: { min: 50, max: 500, step: 50 } },
            { key: "showSubtitles", value: state.showSubtitles, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items.showSubtitles?.title || "显示字幕",
              description: i18n.setting.items.showSubtitles?.description },
            { key: "enableDanmaku", value: state.enableDanmaku, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items.enableDanmaku?.title || "启用弹幕",
              description: i18n.setting.items.enableDanmaku?.description },
            { key: "loopCount", value: state.loopCount, type: "slider" as SettingType, tab: "player",
              title: i18n.setting.items.loopCount.title,
              description: i18n.setting.items.loopCount.description,
              slider: { min: 1, max: 10, step: 1 } },
            { key: "pauseAfterLoop", value: state.pauseAfterLoop, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items.pauseAfterLoop?.title || "片段循环后暂停",
              description: i18n.setting.items.pauseAfterLoop?.description },
            { key: "loopPlaylist", value: state.loopPlaylist, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items?.loopPlaylist?.title || "循环列表",
              description: i18n.setting.items?.loopPlaylist?.description || "播放完列表后从头开始",
              onChange: (v) => (state.loopPlaylist = v, v && (state.loopSingle = false)) },
            { key: "loopSingle", value: state.loopSingle, type: "checkbox" as SettingType, tab: "player",
              title: i18n.setting.items?.loopSingle?.title || "单项循环",
              description: i18n.setting.items?.loopSingle?.description || "重复播放当前媒体",
              onChange: (v) => (state.loopSingle = v, v && (state.loopPlaylist = false)) },
            
            // 通用设置
            { key: "enableDatabase", value: state.enableDatabase, type: "checkbox" as SettingType, tab: "general",
              title: i18n.setting?.items?.enableDatabase?.title || "绑定数据库",
              description: i18n.setting?.items?.enableDatabase?.description || "启用播放列表数据库功能，用于保存和管理媒体项目" },
            { key: "playlistDb", value: state.playlistDb?.id || "", type: "textarea" as SettingType, tab: "general",
              displayCondition: () => state.enableDatabase,
              title: i18n.setting?.items?.playlistDb?.title || "播放列表数据库",
              description: state.playlistDb?.avId
                ? (i18n.setting?.items?.playlistDb?.avIdDescription?.replace('${avId}', state.playlistDb.avId) || `属性视图ID: ${state.playlistDb.avId}`)
                : (i18n.setting?.items?.playlistDb?.description || "输入数据库ID（支持数据库块ID或数据库ID/avid，格式：14位数字-7位字符）"),
              onChange: async (v) => {
                const result = v ? await processDbId(v) : { id: '', avId: '' };
                state.playlistDb = result;
                if (result.avId) await initDb(v).catch(() => {});
                settingItems = createSettings(state);
              },
              rows: 1 },
            { key: "targetDocumentSearch", value: "", type: "text" as SettingType, tab: "general",
              title: i18n.setting.items?.mediaNoteLocation?.search?.title || "媒体笔记创建位置",
              description: i18n.setting.items?.mediaNoteLocation?.search?.description || "输入关键字后按回车搜索文档",
              onKeydown: async (e) => {
                if (e.key === 'Enter') {
                  const result = await notebook.searchAndUpdate(e.target.value, state, { getConfig, saveConfig });
                  if (result.success && result.docs) {
                    notebookOptions = [...notebooks.map(nb => ({ label: nb.name, value: nb.id })), ...result.docs.map(doc => ({ label: doc.hPath || '无标题', value: doc.path?.split('/').pop()?.replace('.sy', '') || doc.id, notebook: doc.box, path: doc.path?.replace('.sy', '') || '' }))];
                    settingItems = createSettings(state);
                  }
                }
              } },
            { key: "targetNotebook", value: state.parentDoc?.id || state.notebook?.id || "", type: "select" as SettingType, tab: "general",
              title: i18n.setting.items?.mediaNoteLocation?.target?.title || "媒体笔记目标笔记本/文档",
              description: state.parentDoc?.id ? `目标文档：${state.parentDoc.name}` : (state.notebook?.id ? `目标笔记本：${state.notebook.name}` : (i18n.setting.items?.mediaNoteLocation?.target?.description || "选择创建媒体笔记的目标笔记本")),
              onChange: (v) => {
                const notebook = notebooks.find(nb => nb.id === v);
                const docOption = notebookOptions.find(opt => opt.value === v);
                if (notebook) { state.notebook = { id: v, name: notebook.name }; state.parentDoc = { id: '', name: '', path: '' }; }
                else if (docOption) { state.parentDoc = { id: v, name: docOption.label, path: docOption.path || '' }; state.notebook = { id: docOption.notebook || '', name: '' }; }
              },
              options: notebookOptions.length ? notebookOptions : notebooks.map(nb => ({ label: nb.name, value: nb.id })) },
            { key: "insertMode", value: state.insertMode, type: "select" as SettingType, tab: "general",
              title: i18n.setting.items.insertMode?.title || "插入方式",
              description: i18n.setting.items.insertMode?.description || "选择时间戳和笔记的插入方式",
              onChange: (v) => state.insertMode = v,
              options: [
                { label: i18n.setting.items.insertMode?.insertBlock || "插入光标处", value: "insertBlock" },
                { label: i18n.setting.items.insertMode?.appendBlock || "追加到块末尾", value: "appendBlock" },
                { label: i18n.setting.items.insertMode?.prependBlock || "添加到块开头", value: "prependBlock" },
                { label: i18n.setting.items.insertMode?.updateBlock || "更新当前块", value: "updateBlock" },
                { label: i18n.setting.items.insertMode?.prependDoc || "插入到文档顶部", value: "prependDoc" },
                { label: i18n.setting.items.insertMode?.appendDoc || "插入到文档底部", value: "appendDoc" },
                { label: i18n.setting.items.insertMode?.clipboard || "复制到剪贴板", value: "clipboard" }
              ] },
            { key: "screenshotWithTimestamp", value: state.screenshotWithTimestamp, type: "checkbox" as SettingType, tab: "general",
              title: i18n.setting.items?.screenshotWithTimestamp?.title || "截图包含时间戳",
              description: i18n.setting.items?.screenshotWithTimestamp?.description || "启用后，截图功能也会添加时间戳链接" },
            { key: "linkFormat", value: state.linkFormat || "- [😄标题 艺术家 字幕 时间](链接)", 
              type: "textarea" as SettingType, tab: "general",
              title: i18n.setting.items?.linkFormat?.title || "链接格式",
              description: i18n.setting.items?.linkFormat?.description || "支持变量：标题、时间、艺术家、链接、字幕、截图",
              rows: 1 },
            { key: "mediaNotesTemplate",
              value: state.mediaNotesTemplate || "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n- 🔗 链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容：",
              type: "textarea" as SettingType, tab: "general",
              title: i18n.setting.items?.mediaNotesTemplate?.title || "媒体笔记模板",
              description: i18n.setting.items?.mediaNotesTemplate?.description || "支持变量：标题、时间、艺术家、链接、时长、封面、类型、ID、日期、时间戳",
              rows: 9 }
        ];
    }

    // 初始化
    async function refreshSettings() {
        const cfg = await getConfig();
        state = { ...DEFAULTS, ...(cfg.settings || {}) };
        // 加载许可证
        currentLicense = await LicenseManager.load(plugin);

        // 体验会员启动提示（激励购买）
        if (currentLicense?.type === 'trial') {
            const expireDate = new Date(currentLicense.expiresAt).toLocaleDateString();
            showMessage(`体验会员已激活（到期：${expireDate}）🎯 升级享受完整功能`, 4000, 'info');
        }
        try { notebooks = await notebook.getList?.() || []; } catch {}
        notebookOptions = [
            ...notebooks.map(nb => ({ label: nb.name, value: nb.id })),
            ...(state.parentDoc?.id ? [{ label: state.parentDoc.name, value: state.parentDoc.id, path: state.parentDoc.path }] : [])
        ];
        loadAccounts();
        settingItems = createSettings(state);
    }


    // 重置单个设置项
    function resetItem(key) {
        state[key] = DEFAULTS[key];
        settingItems = createSettings(state);
    }

    // 设置项变更处理
    async function handleChange(e, item) {
        const v = e.target.type === 'checkbox' 
            ? e.target.checked 
            : e.target.value;
        if (item.onChange) {await item.onChange(v);} 
        else {state[item.key] = v;}
        settingItems = createSettings(state);
        const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
    }

    $: if (activeTab) refreshSettings();

    onMount(refreshSettings);
</script>

<div class="panel common-panel" data-name={group}>
    <!-- 统一导航 -->
    <Tabs {activeTabId} {i18n}>
        <svelte:fragment slot="controls">
            <span class="panel-count">{tabs.find(tab => tab.id === activeTab)?.name || i18n.setting.description}</span>
        </svelte:fragment>
    </Tabs>

    <div class="panel-tabs">
        {#each tabs as tab}
            <button class="tab" class:active={activeTab === tab.id} on:click={() => activeTab = tab.id}>{tab.name}</button>
        {/each}
    </div>

    <div class="panel-content">
        <!-- 账号管理界面 -->
        {#if activeTab === 'account'}
            <!-- Pro设置 -->
            {#each settingItems as item (item.key)}
                {#if item.tab === activeTab && (!item.displayCondition || item.displayCondition(state))}
                <div class="setting-item setting-item-{item.type}" data-key={item.key}>
                    <div class="setting-info">
                        <div class="setting-title">{item.title}</div>
                        {#if item.description}
                            <div class="setting-description {item.description?.icon ? 'acc-desc' : ''}">
                                {@html renderDesc(item.description)}
                            </div>
                        {/if}

                        {#if item.type === 'text'}
                            <input
                                type="text"
                                class="b3-text-field fn__block"
                                value={String(item.value)}
                                on:input={(e) => handleChange(e, item)}
                                on:keydown={(e) => item.onKeydown && item.onKeydown(e)}>
                            <span class="clear-icon" on:click={() => resetItem(item.key)}>
                                <svg class="icon"><use xlink:href="#iconRefresh"></use></svg>
                            </span>
                        {:else if item.type === 'images'}
                            <div class="image-gallery">
                                {#each Array.isArray(item.value) ? item.value : [] as image}
                                    <img src={image.url} alt={image.caption || item.title} class="image-item">
                                {/each}
                            </div>
                        {/if}
                    </div>

                    <div class="setting-control">
                        {#if item.type === 'checkbox'}
                            <label class="checkbox-wrapper">
                                <input type="checkbox" checked={Boolean(item.value)} on:change={(e) => handleChange(e, item)}>
                                <span class="checkbox-custom"></span>
                            </label>
                        {:else if item.type === 'button'}
                            <button class="b3-button b3-button--outline {item.buttonStyle || ''}" on:click={() => item.onAction && item.onAction()}>
                                {item.buttonText || '操作'}
                            </button>
                        {/if}
                    </div>
                </div>
                {/if}
            {/each}

            <!-- 账号列表 -->
            <div class="account-section">
                <div class="account-header">
                    <h3>其他账号</h3>
                    <button class="add-account-btn" on:click={() => showAddMenu = !showAddMenu}>
                        <svg class="icon"><use xlink:href="#iconAdd"></use></svg>
                    </button>
                    {#if showAddMenu}
                        <div class="add-menu">
                            {#each Object.entries(getAccountTypes()) as [type, config]}
                                <div class="menu-item" on:click={() => { editingAccount = 'new_' + type; showAddMenu = false; }}>
                                    {#if config.icon.startsWith('#')}
                                        <svg class="icon"><use xlink:href="{config.icon}"></use></svg>
                                    {:else}
                                        <img src="{config.icon}" alt="{config.name}" class="account-icon">
                                    {/if}
                                    <span>{config.name}</span>
                                </div>
                            {/each}
                        </div>
                    {/if}
                </div>

                <!-- 账号项列表 -->
                {#each accounts as account (account.id)}
                    <div class="account-item">
                        <div class="account-info">
                            {#if getAccountTypes()[account.type]?.icon.startsWith('#')}
                                <svg class="icon account-icon"><use xlink:href="{getAccountTypes()[account.type].icon}"></use></svg>
                            {:else}
                                <img src="{getAccountTypes()[account.type]?.icon}" alt="{getAccountTypes()[account.type]?.name}" class="account-icon">
                            {/if}
                            <div class="account-details">
                                <div class="account-name">{account.name || account.uname || account.username || getAccountTypes()[account.type]?.name}</div>
                                <div class="account-status">
                                    {#if account.type === 'bilibili'}
                                        LV{account.level_info?.current_level} · UID {account.mid}
                                    {:else}
                                        {account.server || '未配置'}
                                    {/if}
                                </div>
                            </div>
                        </div>
                        <div class="account-actions">
                            <button class="action-btn" on:click={(e) => {
                                e.stopPropagation();
                                const menu = new Menu('accountMenu');
                                menu.addItem({ icon: 'iconEdit', label: '编辑', click: () => editingAccount = account.id });
                                menu.addItem({ icon: 'iconTrashcan', label: '删除', click: () => deleteAccount(account.id) });
                                menu.open({ x: e.clientX, y: e.clientY });
                            }}>
                                <svg class="icon"><use xlink:href="#iconMore"></use></svg>
                            </button>
                        </div>
                    </div>
                {/each}
            </div>

            <!-- 编辑账号表单 -->
            {#if editingAccount}
                <div class="account-form-overlay" on:click={() => editingAccount = null}>
                    <div class="account-form" on:click|stopPropagation>
                        <div class="form-header">
                            <h3>{isNewAccount ? '添加' : '编辑'}{getAccountTypes()[editAccountType]?.name}账号</h3>
                            <button class="close-btn" on:click={() => editingAccount = null}>×</button>
                        </div>

                        {#if editAccountType === 'bilibili'}
                            <div class="form-content">
                                <p>请扫描二维码登录B站账号</p>
                                {#if qrcode?.data}
                                    <img src={qrcode.data} alt="B站登录二维码" class="qr-code">
                                    <p class="qr-status">{qrcode.message || '等待扫码'}</p>
                                {:else}
                                    <button class="b3-button b3-button--outline" on:click={async () => {
                                        qrCodeManager ||= new QRCodeManager(
                                            q => qrcode = q,
                                            async loginData => {
                                                state.bilibiliLogin = loginData;
                                                const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
                                                loadAccounts();
                                                editingAccount = null;
                                                qrCodeManager?.stopPolling();
                                                showMessage('B站账号登录成功', 2000, 'info');
                                            }
                                        );
                                        await qrCodeManager.startLogin();
                                    }}>开始登录</button>
                                {/if}
                            </div>
                        {:else}
                            <div class="form-content">
                                {#each getAccountTypes()[editAccountType]?.fields || [] as field}
                                    <div class="form-field">
                                        <label>{field === 'server' ? '服务器地址' : field === 'username' ? '用户名' : field === 'password' ? '密码' : field}</label>
                                        {#if field === 'password'}
                                            <input
                                                type="password"
                                                class="b3-text-field"
                                                bind:value={currentEditAccount[field]}
                                                placeholder="密码"
                                            >
                                        {:else}
                                            <input
                                                type="text"
                                                class="b3-text-field"
                                                bind:value={currentEditAccount[field]}
                                                placeholder={field === 'server' ? 'https://your-server.com' : '用户名'}
                                            >
                                        {/if}
                                    </div>
                                {/each}

                                <div class="form-actions">
                                    <button class="b3-button b3-button--cancel" on:click={() => editingAccount = null}>取消</button>
                                    <button class="b3-button b3-button--text" on:click={async () => {
                                        const result = await validateAccount(currentEditAccount);
                                        if (result.success) {
                                            await saveAccount({ ...currentEditAccount, id: isNewAccount ? undefined : editingAccount });
                                            showMessage('账号保存成功', 2000, 'info');
                                        } else {
                                            showMessage(result.message, 3000, 'error');
                                        }
                                    }}>保存</button>
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            {/if}
        {:else}
            <!-- 其他标签页的设置项 -->
            {#each settingItems as item (item.key)}
                {#if item.tab === activeTab && (!item.displayCondition || item.displayCondition(state))}
                <div class="setting-item setting-item-{item.type}" data-key={item.key}>
                    <div class="setting-info">
                        <div class="setting-title">{item.title}</div>
                        {#if item.description}
                            <div class="setting-description {item.description?.icon ? 'acc-desc' : ''}">
                                {@html renderDesc(item.description)}
                            </div>
                        {/if}
                    
                    {#if item.type === 'slider'}
                        <div class="slider-wrapper">
                            <input type="range"
                                min={item.slider?.min ?? 0}
                                max={item.slider?.max ?? 100}
                                step={item.slider?.step ?? 1}
                                value={state[item.key]}
                                on:input={(e) => handleChange(e, item)}>
                            <span class="slider-value">{item.key === 'speed' ? Number(state[item.key]) / 100 + 'x' : state[item.key]}</span>
                        </div>
                    {:else if item.type === 'text'}
                        <input
                            type="text"
                            class="b3-text-field fn__block"
                            value={String(item.value)}
                            on:input={(e) => handleChange(e, item)}
                            on:keydown={(e) => item.onKeydown && item.onKeydown(e)}>
                        <span class="clear-icon" on:click={() => resetItem(item.key)}>
                            <svg class="icon"><use xlink:href="#iconRefresh"></use></svg>
                        </span>
                    {:else if item.type === 'textarea'}
                        <textarea
                            class="b3-text-field fn__block"
                            rows={item.rows || 4}
                            value={String(item.value)}
                            on:input={(e) => handleChange(e, item)}></textarea>
                        <span class="clear-icon" on:click={() => resetItem(item.key)}>
                            <svg class="icon"><use xlink:href="#iconRefresh"></use></svg>
                        </span>
                    {:else if item.type === 'images'}
                        <div class="image-gallery">
                            {#each Array.isArray(item.value) ? item.value : [] as image}
                                <img src={image.url} alt={image.caption || item.title} class="image-item">
                            {/each}
                        </div>
                    {/if}
                </div>
                
                <div class="setting-control">
                    {#if item.type === 'checkbox'}
                        <label class="checkbox-wrapper">
                            <input type="checkbox" checked={Boolean(item.value)} on:change={(e) => handleChange(e, item)}>
                            <span class="checkbox-custom"></span>
                        </label>
                    {:else if item.type === 'select'}
                        <select class="select-wrapper" style="max-width: 200px; width: 200px;" value={item.value} on:change={(e) => handleChange(e, item)}>
                            {#each item.options || [] as option}
                                <option value={option.value} title={option.label}>{option.label.length > 30 ? option.label.slice(0, 30) + '...' : option.label}</option>
                            {/each}
                        </select>
                    {:else if item.type === 'button'}
                        <button class="b3-button b3-button--outline {item.buttonStyle || ''}" on:click={() => item.onAction && item.onAction()}>
                            {item.buttonText || '操作'}
                        </button>
                    {/if}
                </div>
            </div>
            {/if}
        {/each}
        {/if}
    </div>
</div>

<style>
    /* 账号管理样式 */
    .account-section {
        margin-top: 20px;
        border-top: 1px solid var(--b3-border-color);
        padding-top: 20px;
    }

    .account-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        position: relative;
    }

    .account-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
    }

    .add-account-btn {
        width: 32px;
        height: 32px;
        border: 1px solid var(--b3-border-color);
        border-radius: 4px;
        background: var(--b3-theme-background);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
    }

    .add-account-btn:hover {
        background: var(--b3-theme-background-light);
        border-color: var(--b3-theme-primary);
    }

    .add-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: var(--b3-theme-background);
        border: 1px solid var(--b3-border-color);
        border-radius: 4px;
        box-shadow: var(--b3-point-shadow);
        z-index: 1000;
        min-width: 120px;
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: background 0.2s;
    }

    .menu-item:hover {
        background: var(--b3-theme-background-light);
    }

    .menu-item .icon, .menu-item .account-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
    }

    .account-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        border: 1px solid var(--b3-border-color);
        border-radius: 4px;
        margin-bottom: 8px;
        background: var(--b3-theme-background);
    }

    .account-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .account-info .account-icon {
        width: 24px;
        height: 24px;
        margin-right: 12px;
        border-radius: 4px;
    }

    .account-details {
        flex: 1;
    }

    .account-name {
        font-weight: 500;
        margin-bottom: 2px;
    }

    .account-status {
        font-size: 12px;
        color: var(--b3-theme-on-surface-light);
    }

    .action-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
    }

    .action-btn:hover {
        background: var(--b3-theme-background-light);
    }

    .account-form-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }

    .account-form {
        background: var(--b3-theme-background);
        border-radius: 8px;
        width: 400px;
        max-width: 90vw;
        max-height: 80vh;
        overflow: auto;
    }

    .form-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid var(--b3-border-color);
    }

    .form-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
    }

    .close-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        cursor: pointer;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .form-content {
        padding: 20px;
    }

    .form-field {
        margin-bottom: 16px;
    }

    .form-field label {
        display: block;
        margin-bottom: 4px;
        font-size: 14px;
        font-weight: 500;
    }

    .form-field input {
        width: 100%;
    }

    .form-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-top: 20px;
    }

    .qr-code {
        display: block;
        margin: 16px auto;
        border-radius: 4px;
    }

    .qr-status {
        text-align: center;
        color: var(--b3-theme-on-surface-light);
        margin-top: 8px;
    }
</style>