<script lang="ts">
    import { onMount } from "svelte";
    import { showMessage, getFrontend, Menu } from "siyuan";
    import type { ISettingItem, SettingType } from "../core/types";
    import Tabs from './Tabs.svelte';
    import { notebook } from "../core/document";
    import { QRCodeManager, isBilibiliAvailable } from "../core/bilibili";
    import { LicenseManager, type LicenseInfo } from "../core/license";
    import { WebDAVManager } from "../core/webdav";

    export let group: string;
    export let config: any;
    export let i18n: any;
    export let activeTabId = 'settings';
    export let plugin: any;

    // 配置管理
    const getConfig = async () => await plugin.loadData('config.json') || {};
    const saveConfig = async (cfg) => { await plugin.saveData('config.json', cfg, 2); window.dispatchEvent(new CustomEvent('configUpdated', { detail: cfg })); };
    const processDbId = async (id: string) => { if (!id || !/^\d{14}-[a-z0-9]{7}$/.test(id)) return { id, avId: '' }; const avId = (await fetch('/api/query/sql', { method: 'POST', body: JSON.stringify({ stmt: `SELECT markdown FROM blocks WHERE type='av' AND id='${id}'` }) }).then(r => r.json()).catch(() => ({ data: [] }))).data?.[0]?.markdown?.match(/data-av-id="([^"]+)"/)?.[1]; return { id, avId: avId || id }; };
    const initDb = async (id: string) => { try { const { avId } = await processDbId(id); return !!(await fetch('/api/av/getAttributeView', { method: 'POST', body: JSON.stringify({ id: avId }) }).then(r => r.json()).catch(() => ({ code: -1 }))).code === 0; } catch { return false; } };

    // 状态管理
    let state = {
        openMode: "default", playerType: "built-in", playerPath: "PotPlayerMini64.exe", volume: 70, speed: 100,
        showSubtitles: false, enableDanmaku: false, loopCount: 3, pauseAfterLoop: false, loopPlaylist: false, loopSingle: false,
        insertMode: "updateBlock", notebook: { id: '', name: '' }, parentDoc: { id: '', name: '', path: '' },
        enableDatabase: false, playlistDb: { id: '', avId: '' }, webdavAccounts: [], openlistAccounts: [], bilibiliAccounts: [],
        playlistView: { mode: 'detailed', tab: '目录', expanded: [] }, screenshotWithTimestamp: false,
        linkFormat: "- [😄标题 艺术家 字幕 时间](链接)",
        mediaNotesTemplate: "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n-  链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容："
    };

    // 全局状态
    let activeTab = 'account', settingItems = [], notebooks = [], notebookOptions = [], accounts = [], editingAccount = null;
    let qrCodeManager = null, qrcode = { data: '', key: '' }, licenseCode = '', currentLicense = null;

    // 账号类型
    const ACCOUNT_TYPES = {
        webdav: { name: 'WebDAV', fields: ['server', 'username', 'password'] },
        openlist: { name: 'OpenList', fields: ['server', 'username', 'password'] },
        ...(isBilibiliAvailable() ? { bilibili: { name: 'B站', fields: [] } } : {})
    };

    // 账号管理
    const loadAccounts = () => accounts = [...(state.webdavAccounts || []).map(acc => ({ ...acc, type: 'webdav' })), ...(state.openlistAccounts || []).map(acc => ({ ...acc, type: 'openlist' })), ...(state.bilibiliAccounts || []).map(acc => ({ ...acc, type: 'bilibili' }))];

    const saveAccount = async (type, data, isEdit) => {
        if (type === 'webdav' && !(await WebDAVManager.checkConnection(data)).connected) return showMessage((await WebDAVManager.checkConnection(data)).message, 3000, 'error');
        if (type !== 'webdav' && (!data.server || !data.username)) return showMessage('服务器地址和用户名不能为空', 3000, 'error');
        const key = type + 'Accounts'; state[key] = state[key] || [];
        isEdit ? (state[key][state[key].findIndex(a => a.id === editingAccount)] = { ...state[key][state[key].findIndex(a => a.id === editingAccount)], ...data }) : state[key].push({ ...data, id: Date.now().toString() });
        const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg); loadAccounts(); settingItems = createSettings(state);
        showMessage(`${ACCOUNT_TYPES[type]?.name}保存成功`, 2000, 'info'); editingAccount = null; state.editingData = {};
    };

    // 统一操作
    const handleAction = (action, data) => {
        const type = data.type;
        if (action === 'edit') { editingAccount = type === 'pro' ? 'pro' : data.id; if (type !== 'pro' && type !== 'bilibili') state.editingData = { ...data }; }
        else if (action === 'delete') {
            if (type === 'pro') currentLicense = null;
            else state[type + 'Accounts'] = (state[type + 'Accounts'] || []).filter(a => a.id !== data.id);
            if (type !== 'pro') { getConfig().then(c => { c.settings = state; saveConfig(c); }); loadAccounts(); }
            settingItems = createSettings(state); showMessage(type === 'pro' ? 'Pro功能已关闭' : '账号已删除', 2000, 'info');
        } else if (action === 'relogin') {
            editingAccount = 'bilibili_' + Date.now(); (qrCodeManager ||= new QRCodeManager(q => qrcode = q, async d => {
                const newAccount = { ...d, id: 'bili_' + d.mid };
                state.bilibiliAccounts = state.bilibiliAccounts || [];
                const existingIndex = state.bilibiliAccounts.findIndex(a => a.mid === d.mid);
                if (existingIndex >= 0) state.bilibiliAccounts[existingIndex] = newAccount;
                else state.bilibiliAccounts.push(newAccount);
                const c = await getConfig(); c.settings = state; await saveConfig(c);
                loadAccounts(); settingItems = createSettings(state); editingAccount = null; qrCodeManager?.stopPolling();
                showMessage('B站登录成功', 2000, 'info');
            })).startLogin();
        }
    };
    const activatePro = async (code = licenseCode) => { const r = await LicenseManager.activate(code, plugin); currentLicense = r.license || null; if (r.success && licenseCode) licenseCode = ''; showMessage(r.message || r.error, r.success ? 2000 : (r.isHtml ? 0 : 3000), r.success ? 'info' : 'error'); settingItems = createSettings(state); const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg); editingAccount = null; };

    // 配置
    const tabs = [{ id: 'account', name: '账号' }, { id: 'player', name: '播放器' }, { id: 'general', name: '通用' }];
    const accDesc = (icon, name, status, statusColor, info1, info2) => ({ icon, name, status, statusColor, info1, info2 });
    const renderDesc = (d) => d?.icon ? `${d.icon.startsWith('#') ? `<svg class="acc-icon"><use xlink:href="${d.icon}"></use></svg>` : `<img src="${d.icon}" class="acc-icon">`}<div class="acc-info"><b>${d.name}</b> <span style="color:${d.statusColor}">${d.status}</span><br><small>${d.info1}</small><br><small class="acc-muted">${d.info2}</small></div>` : d;

    // 创建设置项
    const createSettings = (state) => [
        // 账号
        { key: "proLicense", type: "account", tab: "account", description: currentLicense?.isValid ? accDesc((currentLicense.type === 'dragon' ? '#iconDragon' : '#iconVIP'), `${currentLicense.userName} (${currentLicense.type})`, '', '', `用户ID: ${currentLicense.userId}`, `期限: ${currentLicense.expiresAt === 0 ? '永久有效' : new Date(currentLicense.expiresAt).toLocaleDateString()}`) : accDesc('#iconVIP', '申请Pro会员', '', '', '享受完整功能体验', '激活码或申请体验'), accountData: { type: 'pro', id: 'pro', isValid: !!currentLicense?.isValid } },
        ...accounts.sort((a, b) => ({ webdav: 1, openlist: 2, bilibili: 3 }[a.type] || 999) - ({ webdav: 1, openlist: 2, bilibili: 3 }[b.type] || 999)).map(acc => ({ key: `account_${acc.id}`, type: "account", tab: "account", description: accDesc(acc.type === 'bilibili' ? acc.face || '#iconBili' : '#iconCloud', acc.uname || acc.username || ACCOUNT_TYPES[acc.type]?.name, acc.type === 'bilibili' ? `LV${acc.level_info?.current_level}` : '已连接', '#4caf50', acc.type === 'bilibili' ? `UID ${acc.mid}` : acc.server, acc.type === 'bilibili' ? `硬币 ${acc.money}` : `用户: ${acc.username}`), accountData: acc })),
        // 播放器
        { key: "openMode", value: state.openMode, type: "select", tab: "player", title: "打开方式", displayCondition: () => !getFrontend().endsWith('mobile'), options: [{ label: "新标签", value: "default" }, { label: "右侧新标签", value: "right" }, { label: "底部新标签", value: "bottom" }, { label: "新窗口", value: "window" }] },
        { key: "playerType", value: state.playerType, type: "select", tab: "player", title: "播放器类型", options: [{ label: "内置播放器", value: "built-in" }, { label: "PotPlayer", value: "potplayer" }, { label: "浏览器", value: "browser" }] },
        { key: "playerPath", value: state.playerPath, type: "textarea", tab: "player", displayCondition: () => settingItems.find(i => i.key === 'playerType')?.value === 'potplayer', title: "PotPlayer路径", rows: 1 },
        { key: "volume", value: state.volume, type: "slider", tab: "player", title: "音量", slider: { min: 0, max: 100, step: 1 } },
        { key: "speed", value: state.speed, type: "slider", tab: "player", title: "播放速度", slider: { min: 50, max: 500, step: 50 } },
        { key: "showSubtitles", value: state.showSubtitles, type: "checkbox", tab: "player", title: "显示字幕" },
        { key: "enableDanmaku", value: state.enableDanmaku, type: "checkbox", tab: "player", title: "启用弹幕" },
        { key: "loopCount", value: state.loopCount, type: "slider", tab: "player", title: "循环次数", slider: { min: 1, max: 10, step: 1 } },
        { key: "pauseAfterLoop", value: state.pauseAfterLoop, type: "checkbox", tab: "player", title: "片段循环后暂停" },
        { key: "loopPlaylist", value: state.loopPlaylist, type: "checkbox", tab: "player", title: "循环列表", onChange: (v) => (state.loopPlaylist = v, v && (state.loopSingle = false)) },
        { key: "loopSingle", value: state.loopSingle, type: "checkbox", tab: "player", title: "单项循环", onChange: (v) => (state.loopSingle = v, v && (state.loopPlaylist = false)) },
        // 通用
        { key: "enableDatabase", value: state.enableDatabase, type: "checkbox", tab: "general", title: "绑定数据库" },
        { key: "playlistDb", value: state.playlistDb?.id || "", type: "textarea", tab: "general", displayCondition: () => state.enableDatabase, title: "播放列表数据库", description: state.playlistDb?.avId ? `属性视图ID: ${state.playlistDb.avId}` : "输入数据库ID", onChange: async (v) => { const result = v ? await processDbId(v) : { id: '', avId: '' }; state.playlistDb = result; if (result.avId) await initDb(v).catch(() => {}); settingItems = createSettings(state); }, rows: 1 },
        { key: "targetDocumentSearch", value: "", type: "text", tab: "general", title: "媒体笔记创建位置", description: "输入关键字后按回车搜索文档", onKeydown: async (e) => { if (e.key === 'Enter') { const result = await notebook.searchAndUpdate(e.target.value, state, { getConfig, saveConfig }); if (result.success && result.docs) { notebookOptions = [...notebooks.map(nb => ({ label: nb.name, value: nb.id })), ...result.docs.map(doc => ({ label: doc.hPath || '无标题', value: doc.path?.split('/').pop()?.replace('.sy', '') || doc.id, notebook: doc.box, path: doc.path?.replace('.sy', '') || '' }))]; settingItems = createSettings(state); } } } },
        { key: "targetNotebook", value: state.parentDoc?.id || state.notebook?.id || "", type: "select", tab: "general", title: "目标笔记本/文档", description: state.parentDoc?.id ? `目标文档：${state.parentDoc.name}` : (state.notebook?.id ? `目标笔记本：${state.notebook.name}` : "选择创建媒体笔记的目标笔记本"), onChange: (v) => { const nb = notebooks.find(nb => nb.id === v); const doc = notebookOptions.find(opt => opt.value === v); if (nb) { state.notebook = { id: v, name: nb.name }; state.parentDoc = { id: '', name: '', path: '' }; } else if (doc) { state.parentDoc = { id: v, name: doc.label, path: doc.path || '' }; state.notebook = { id: doc.notebook || '', name: '' }; } }, options: notebookOptions.length ? notebookOptions : notebooks.map(nb => ({ label: nb.name, value: nb.id })) },
        { key: "insertMode", value: state.insertMode, type: "select", tab: "general", title: "插入方式", onChange: (v) => state.insertMode = v, options: [{ label: "插入光标处", value: "insertBlock" }, { label: "追加到块末尾", value: "appendBlock" }, { label: "添加到块开头", value: "prependBlock" }, { label: "更新当前块", value: "updateBlock" }, { label: "插入到文档顶部", value: "prependDoc" }, { label: "插入到文档底部", value: "appendDoc" }, { label: "复制到剪贴板", value: "clipboard" }] },
        { key: "screenshotWithTimestamp", value: state.screenshotWithTimestamp, type: "checkbox", tab: "general", title: "截图包含时间戳" },
        { key: "linkFormat", value: state.linkFormat || "- [😄标题 艺术家 字幕 时间](链接)", type: "textarea", tab: "general", title: "链接格式", description: "支持变量：标题、时间、艺术家、链接、字幕、截图", rows: 1 },
        { key: "mediaNotesTemplate", value: state.mediaNotesTemplate || "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n- 🔗 链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容：", type: "textarea", tab: "general", title: "媒体笔记模板", description: "支持变量：标题、时间、艺术家、链接、时长、封面、类型、ID、日期、时间戳", rows: 9 }
    ];

    // 初始化
    const refreshSettings = async () => { const cfg = await getConfig(); state = { ...state, ...(cfg.settings || {}) }; currentLicense = await LicenseManager.load(plugin); if (currentLicense?.type === 'trial') showMessage(`体验会员已激活（到期：${new Date(currentLicense.expiresAt).toLocaleDateString()}）🎯 升级享受完整功能`, 4000, 'info'); try { notebooks = await notebook.getList?.() || []; } catch {} notebookOptions = [...notebooks.map(nb => ({ label: nb.name, value: nb.id })), ...(state.parentDoc?.id ? [{ label: state.parentDoc.name, value: state.parentDoc.id, path: state.parentDoc.path }] : [])]; loadAccounts(); settingItems = createSettings(state); };
    const resetItem = (key) => { state[key] = state[key]; settingItems = createSettings(state); };
    const handleChange = async (e, item) => { const v = e.target.type === 'checkbox' ? e.target.checked : e.target.value; if (item.onChange) await item.onChange(v); else state[item.key] = v; settingItems = createSettings(state);
        const cfg = await getConfig(); cfg.settings = state; await saveConfig(cfg);
    }

    $: if (activeTab) refreshSettings();
    onMount(refreshSettings);
    (globalThis as any).copyUserInfo = async () => { const user = await LicenseManager.getSiYuanUserInfo(); if (!user) return showMessage('请先登录思源账号', 3000, 'error'); await navigator.clipboard.writeText(JSON.stringify({userId: user.userId, userName: user.userName})); showMessage('用户信息已复制', 2000, 'info'); };
</script>

<div class="panel common-panel" data-name={group}>
    <!-- 统一导航 -->
    <Tabs {activeTabId} {i18n}>
        <svelte:fragment slot="controls">
            <span class="panel-count">{tabs.find(tab => tab.id === activeTab)?.name || i18n.setting.description}</span>
        </svelte:fragment>
    </Tabs>

    <div class="panel-tabs">
        {#each tabs as tab}
            <button class="tab" class:active={activeTab === tab.id} on:click={() => activeTab = tab.id}>{tab.name}</button>
        {/each}
    </div>

    <div class="panel-content">
        {#each settingItems as item (item.key)}
            {#if item.tab === activeTab && (!item.displayCondition || item.displayCondition(state))}
            <!-- 分组标题 -->
            {#if item.type === 'account' && item.accountData?.type !== 'pro'}
                {@const prev = settingItems[settingItems.indexOf(item) - 1]}
                {#if !prev?.accountData || prev.accountData.type !== item.accountData.type}
                    <div style="margin: 20px 0 8px 0; color: var(--b3-theme-on-surface-light); font-size: 14px; font-weight: 500;">{ACCOUNT_TYPES[item.accountData.type]?.name}</div>
                {/if}
            {/if}

            <div class="setting-item setting-item-{item.type}" data-key={item.key}>
                <div class="setting-info">
                    {#if item.type !== 'account'}<div class="setting-title">{item.title}</div>{/if}
                    {#if item.description}<div class="setting-description {item.description?.icon ? 'acc-desc' : ''}">{@html renderDesc(item.description)}</div>{/if}
                    {#if item.type === 'slider'}<div class="slider-wrapper"><input type="range" min={item.slider?.min ?? 0} max={item.slider?.max ?? 100} step={item.slider?.step ?? 1} value={state[item.key]} on:input={(e) => handleChange(e, item)}><span class="slider-value">{item.key === 'speed' ? Number(state[item.key]) / 100 + 'x' : state[item.key]}</span></div>
                    {:else if item.type === 'text'}<input type="text" class="b3-text-field fn__block" value={String(item.value)} on:input={(e) => handleChange(e, item)} on:keydown={(e) => item.onKeydown && item.onKeydown(e)}><span class="clear-icon" on:click={() => resetItem(item.key)}><svg class="icon"><use xlink:href="#iconRefresh"></use></svg></span>
                    {:else if item.type === 'textarea'}<textarea class="b3-text-field fn__block" rows={item.rows || 4} value={String(item.value)} on:input={(e) => handleChange(e, item)}></textarea><span class="clear-icon" on:click={() => resetItem(item.key)}><svg class="icon"><use xlink:href="#iconRefresh"></use></svg></span>
                    {/if}
                </div>
                <div class="setting-control">
                    {#if item.type === 'checkbox'}<label class="checkbox-wrapper"><input type="checkbox" checked={Boolean(item.value)} on:change={(e) => handleChange(e, item)}><span class="checkbox-custom"></span></label>
                    {:else if item.type === 'select'}<select class="select-wrapper" style="max-width: 200px; width: 200px;" value={item.value} on:change={(e) => handleChange(e, item)}>{#each item.options || [] as option}<option value={option.value} title={option.label}>{option.label.length > 30 ? option.label.slice(0, 30) + '...' : option.label}</option>{/each}</select>
                    {:else if item.type === 'button'}<button class="b3-button b3-button--outline {item.buttonStyle || ''}" on:click={() => item.onAction && item.onAction()}>{item.buttonText || '操作'}</button>
                    {:else if item.type === 'account'}<button class="b3-button b3-button--text" on:click|preventDefault|stopPropagation={(e) => { const m = new Menu(); const type = item.accountData.type; m.addItem({ icon: 'iconEdit', label: type === 'bilibili' ? '重新登录' : '编辑', click: () => handleAction(type === 'bilibili' ? 'relogin' : 'edit', item.accountData) }); if (type === 'pro' ? item.accountData.isValid : true) m.addItem({ icon: 'iconTrashcan', label: '删除', click: () => handleAction('delete', item.accountData) }); m.open({ x: e.clientX, y: e.clientY }); }}>⋯</button>
                    {/if}
                </div>
            </div>


            {/if}
        {/each}



        <!-- 表单 -->
        {#if editingAccount}
            <div class="setting-item" style="margin: 16px 0; border: 1px solid var(--b3-border-color); border-radius: 4px; padding: 16px;">
                <div class="setting-info">
                    {#if editingAccount === 'pro'}<div class="setting-title">编辑Pro</div><input type="text" class="b3-text-field fn__block" bind:value={licenseCode} placeholder="请输入激活码" style="margin: 8px 0;"><div style="margin-top: 12px;"><button class="b3-button b3-button--text" on:click={activatePro}>保存</button><button class="b3-button b3-button--cancel" on:click={() => editingAccount = null}>取消</button>{#if !currentLicense?.isValid}<button class="b3-button b3-button--cancel" on:click={() => activatePro('')}>申请体验</button>{/if}</div>
                    {:else if editingAccount?.startsWith('bilibili')}<div class="setting-title">B站登录</div><img src={qrcode.data} alt="登录二维码" style="width: 200px; margin: 10px 0;"><p>{qrcode.message || '等待扫码'}</p><div style="margin-top: 12px;"><button class="b3-button b3-button--cancel" on:click={() => editingAccount = null}>取消</button></div>
                    {:else}{@const acc = accounts.find(a => a.id === editingAccount)}{@const type = acc?.type || editingAccount}<div class="setting-title">{acc ? '编辑' : '添加'}{ACCOUNT_TYPES[type]?.name}</div>{#each (ACCOUNT_TYPES[type]?.fields || []) as field}<input type={field === 'password' ? 'password' : 'text'} class="b3-text-field fn__block" style="margin: 8px 0;" value={state.editingData?.[field] || ''} placeholder={field === 'server' ? '服务器地址' : field === 'username' ? '用户名' : '密码'} on:input={(e) => { state.editingData = state.editingData || {}; state.editingData[field] = e.target.value; }}>{/each}<div style="margin-top: 12px;"><button class="b3-button b3-button--text" on:click={() => saveAccount(type, state.editingData || {}, !!acc)}>保存</button><button class="b3-button b3-button--cancel" on:click={() => { editingAccount = null; state.editingData = {}; }}>取消</button></div>
                    {/if}
                </div>
            </div>
        {/if}

        <!-- 添加按钮 -->
        {#if activeTab === 'account'}
            <div class="setting-item" style="margin-top: 20px;">
                <div class="setting-info">
                    <div class="setting-title">添加账号</div>
                </div>
                <div class="setting-control">
                    <button class="b3-button b3-button--outline" on:click|preventDefault|stopPropagation={(e) => {
                        const m = new Menu();
                        m.addItem({ icon: 'iconCloud', label: 'WebDAV', click: () => { editingAccount = 'webdav'; state.editingData = {}; }});
                        m.addItem({ icon: 'iconCloud', label: 'OpenList', click: () => { editingAccount = 'openlist'; state.editingData = {}; }});
                        if (isBilibiliAvailable()) {
                            m.addItem({ icon: 'iconBili', label: 'B站', click: () => handleAction('relogin', { type: 'bilibili' }) });
                        }
                        m.open({ x: e.clientX, y: e.clientY });
                    }}>添加账号</button>
                </div>
            </div>
        {/if}


    </div>
</div>

