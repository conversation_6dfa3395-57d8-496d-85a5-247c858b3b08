# 账号管理系统重构说明

## 重构目标

将原本凌乱的账号界面重构为简洁高效的多账号管理系统：

- 保留Pro许可证功能
- 隐藏其他账号的开关形式
- 使用统一的账号项组件
- 支持多账号管理
- 按类型分组显示

## 核心功能

### 1. Pro许可证管理
- 保持原有的激活码输入和验证功能
- 显示许可证状态和用户信息
- 支持体验版和正式版切换

### 2. 账号项管理
- **添加账号**: Pro区域下方有"+"按钮，点击弹出账号类型菜单
- **账号显示**: 每个账号显示为统一的卡片样式，包含图标、名称、状态
- **账号操作**: 右侧三个点菜单，支持编辑和删除
- **多账号支持**: 同类型可添加多个账号

### 3. 支持的账号类型
- **WebDAV**: 服务器地址、用户名、密码
- **OpenList**: 服务器地址、用户名、密码  
- **B站账号**: 二维码扫码登录

## 技术实现

### 数据结构
```typescript
// 账号类型配置
const ACCOUNT_TYPES = {
    webdav: { name: 'WebDAV', icon: '/path/to/webdav.svg', fields: ['server', 'username', 'password'] },
    openlist: { name: 'OpenList', icon: '/path/to/openlist.svg', fields: ['server', 'username', 'password'] },
    bilibili: { name: 'B站', icon: '#iconBili', fields: [] }
};

// 存储结构
state = {
    webdavAccounts: [{ id: '1', name: '我的WebDAV', server: '...', username: '...', password: '...' }],
    openlistAccounts: [{ id: '2', name: '我的OpenList', server: '...', username: '...', password: '...' }],
    bilibiliLogin: { mid: 123, uname: '用户名', ... }
}
```

### 核心函数
- `loadAccounts()`: 从配置加载所有账号到统一数组
- `saveAccount(account)`: 保存账号到对应类型数组
- `deleteAccount(accountId)`: 删除指定账号
- `validateAccount(account)`: 验证账号连接

### UI组件
- **账号项**: 统一的卡片样式，显示图标、名称、状态
- **添加菜单**: 弹出式菜单选择账号类型
- **编辑表单**: 模态框形式的账号配置表单
- **操作菜单**: 右键菜单支持编辑和删除

## 样式特点

- **极简设计**: 减少视觉噪音，突出核心功能
- **统一风格**: 所有账号项使用相同的卡片样式
- **响应式**: 适配不同屏幕尺寸
- **思源风格**: 使用思源笔记的设计语言和颜色变量

## 使用流程

1. **添加账号**: 点击"+"按钮 → 选择账号类型 → 填写配置 → 验证连接 → 保存
2. **编辑账号**: 点击账号项的"⋯"按钮 → 选择编辑 → 修改配置 → 验证连接 → 保存
3. **删除账号**: 点击账号项的"⋯"按钮 → 选择删除 → 确认删除

## 兼容性

- 保持与现有配置的向后兼容
- 自动迁移旧的单账号配置到新的多账号结构
- 不影响其他功能模块的使用

## 代码优化

- **最少代码**: 复用现有组件和样式
- **极限精简**: 移除冗余的配置项和逻辑
- **高效实现**: 使用Svelte的响应式特性减少手动DOM操作
